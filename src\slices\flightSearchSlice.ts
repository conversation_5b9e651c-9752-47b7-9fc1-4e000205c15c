import type { FlightSearchForm } from '@/types/flights';
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import { format } from 'date-fns';

const initialState: FlightSearchForm = {
  trip_type: 'One Way',
  travel_class: 'Direct',
  adults: 1,
  children: 0,
  infants: 0,
  departure_date: format(new Date(), 'yyyy-MM-dd'),
  return_date: format(new Date(), 'yyyy-MM-dd'),
  destination: { city: '', iata_code: '' },
  origin: { city: '', iata_code: '' },
  direct_only: false,
};

const flightSearchSlice = createSlice({
  name: 'flightSearchForm',
  initialState,
  reducers: {
    // Trip configuration (trip type and travel class)
    updateTripConfig: (
      state,
      action: PayloadAction<{
        trip_type?: string;
        travel_class?: string;
      }>
    ) => {
      if (action.payload.trip_type !== undefined) {
        state.trip_type = action.payload.trip_type;
      }
      if (action.payload.travel_class !== undefined) {
        state.travel_class = action.payload.travel_class;
      }
    },

    updateDirectFlightSearch: (
      state,
      action: PayloadAction<{
        direct_only: boolean;
      }>
    ) => {
      state.direct_only = action.payload.direct_only;
    },

    // Passenger management with validation
    updatePassengers: (
      state,
      action: PayloadAction<{
        adults?: number;
        children?: number;
        infants?: number;
      }>
    ) => {
      if (action.payload.adults !== undefined) {
        state.adults = Math.max(1, action.payload.adults);
      }
      if (action.payload.children !== undefined) {
        state.children = Math.max(0, action.payload.children);
      }
      if (action.payload.infants !== undefined) {
        state.infants = Math.max(0, action.payload.infants);
      }
    },

    // Date management
    updateDates: (
      state,
      action: PayloadAction<{
        departure_date?: string;
        return_date?: string;
      }>
    ) => {
      if (action.payload.departure_date !== undefined) {
        state.departure_date = action.payload.departure_date;
      }
      if (action.payload.return_date !== undefined) {
        state.return_date = action.payload.return_date;
      }
    },

    // Location management
    updateLocations: (
      state,
      action: PayloadAction<{
        origin?: { city: string; iata_code: string };
        destination?: { city: string; iata_code: string };
      }>
    ) => {
      if (action.payload.origin !== undefined) {
        state.origin = action.payload.origin;
      }
      if (action.payload.destination !== undefined) {
        state.destination = action.payload.destination;
      }
    },

    // Update entire form at once
    setFlightSearchForm: (
      state,
      action: PayloadAction<Partial<FlightSearchForm>>
    ) => {
      return { ...state, ...action.payload };
    },

    // Reset form to initial state
    resetFlightSearchForm: () => {
      // Return a new initial state with current date
      return {
        ...initialState,
        departure_date: format(new Date(), 'yyyy-MM-dd'),
        return_date: format(new Date(), 'yyyy-MM-dd'),
      };
    },
  },
});

// Export actions
export const {
  updateTripConfig,
  updatePassengers,
  updateDates,
  updateLocations,
  updateDirectFlightSearch,
  setFlightSearchForm,
  resetFlightSearchForm,
} = flightSearchSlice.actions;

// Export reducer
export default flightSearchSlice.reducer;

// Selectors for easy access to state
export const selectFlightSearchForm = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm;
export const selectTripType = (state: { flightSearchForm: FlightSearchForm }) =>
  state.flightSearchForm.trip_type;
export const selectTravelClass = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm.travel_class;
export const selectPassengerCounts = (state: {
  flightSearchForm: FlightSearchForm;
}) => ({
  adults: state.flightSearchForm.adults,
  children: state.flightSearchForm.children,
  infants: state.flightSearchForm.infants,
});
export const selectTotalPassengers = (state: {
  flightSearchForm: FlightSearchForm;
}) =>
  state.flightSearchForm.adults +
  state.flightSearchForm.children +
  state.flightSearchForm.infants;
export const selectOrigin = (state: { flightSearchForm: FlightSearchForm }) =>
  state.flightSearchForm.origin;
export const selectDestination = (state: {
  flightSearchForm: FlightSearchForm;
}) => state.flightSearchForm.destination;
export const selectDates = (state: { flightSearchForm: FlightSearchForm }) => ({
  departure_date: state.flightSearchForm.departure_date,
  return_date: state.flightSearchForm.return_date,
});
