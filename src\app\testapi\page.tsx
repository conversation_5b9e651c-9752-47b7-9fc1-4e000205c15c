'use client';

import { useGetAllProductsQuery } from '@/services/dummyApi';
import {
  selectPassengerCounts,
  updatePassengers,
} from '@/slices/flightSearchSlice';
import type { RootState } from '@/store/store';
import { useDispatch, useSelector } from 'react-redux';

export default function TestApiPage() {
  const dispatch = useDispatch();
  const searchData = useSelector((state: RootState) => state.flightSearchForm);
  // eslint-disable-next-line no-console
  console.log('searchData', searchData);

  dispatch(updatePassengers({ adults: 2, children: 4, infants: 1 }));
  // eslint-disable-next-line no-console
  console.log(useSelector(selectPassengerCounts));

  const { data, isLoading } = useGetAllProductsQuery();

  // console.log(data)

  if (isLoading) {
    return <>API making call. Loading...</>;
  }

  return data?.products.map(item => (
    <p key={item.id} className="text-green-400 px-4">
      {item.title}
    </p>
  ));
}
