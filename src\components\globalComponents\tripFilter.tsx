'use client';

import { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { Input } from '@heroui/react';
import { apiRequest } from '../../uitls/api';
import { useSession } from 'next-auth/react';
import { GPSIcon, MagniferIcon, Pen2Icon, PointMapIcon } from '../icons';
import Map from 'react-map-gl/mapbox'; // ✅ for v7+
import { Marker } from 'react-map-gl/mapbox';
import Cookies from 'js-cookie';

export default function TripFilter() {
  const [startLocation, setStartLocation] = useState('Wales, United Kingdom');
  const { data: session } = useSession() ?? { data: null };
  const [destination, setDestination] = useState('');
  const [destinations, setDestinations] = useState<
    { destination_id: number; name: string; type: string }[]
  >([]);
  const [isMapVisible, setIsMapVisible] = useState(false);
  const [pickedCoords, setPickedCoords] = useState<{
    lng: number;
    lat: number;
  } | null>(null);
  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        const res = await apiRequest({
          method: 'GET',
          url: '/activities/suggestion',
          endpoint: 'llm',
          params: { query: destination, limit: 10 },
          isGuest: !!Cookies.get('guest_id'),
          tokens: session || Cookies.get('guest_id') || '',
        });

        setDestinations(res.detail.data || []);
      } catch (error) {
        // console.error('Error fetching suggestions:', error);
      }
    };

    fetchSuggestions();
  }, [destination]); // or any other dependency

  const handleRemove = (location: number) => {
    setDestinations(destinations.filter(d => d.destination_id !== location));
  };

  const handleGetCurrentLocation = () => {
    if (!navigator.geolocation) {
      return;
    }

    navigator.geolocation.getCurrentPosition(async position => {
      const { latitude, longitude } = position.coords;

      try {
        // Mapbox Reverse Geocoding
        const res = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=pk.eyJ1Ijoibnh2b3kiLCJhIjoiY204eWl0djVyMDFnMzJrc2Q1M2xhZHdtcyJ9.iz8RLpi6Yy3tTSGa_EKeDA`
        );
        const data = await res.json();

        if (data.features && data.features.length > 0) {
          setStartLocation(data.features[0].place_name);
        } else {
          // console.error("No location found");
          setStartLocation(`${latitude}, ${longitude}`);
        }
      } catch (err) {
        // console.error("Error fetching location", err);
        setStartLocation(`${latitude}, ${longitude}`);
      }
    });
  };

  const handleMapClick = async (e: any) => {
    const { lat, lng } = e.lngLat;

    setPickedCoords({ lat, lng });

    try {
      const res = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}`
      );
      const data = await res.json();

      if (data.features && data.features.length > 0) {
        setStartLocation(data.features[0].place_name);
      } else {
        setStartLocation(`${lat}, ${lng}`);
      }
    } catch (err) {
      // console.error('Error fetching location name:', err);
      setDestination(`${lat}, ${lng}`);
    }
  };

  return (
    <div className="w-full min-w-lg max-w-xl rounded-2xl bg-white p-6 space-y-6">
      {/* Start Location */}
      <div>
        <label className="block text-sm  text-gray mb-1">Start location</label>
        <div className="relative">
          <Input
            labelPlacement="outside"
            placeholder="Enter location"
            endContent={<Pen2Icon />}
            value={startLocation}
            onChange={e => setStartLocation(e.target.value)}
            // type="email"
            variant="bordered"
          />
        </div>
        <div className="mt-3 flex gap-3">
          <button
            type="button"
            onClick={handleGetCurrentLocation}
            className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200  transition"
          >
            <GPSIcon className="h-4 w-4" />
            Current Location
          </button>
          <button
            type="button"
            onClick={() => {
              setIsMapVisible(!isMapVisible);
            }}
            className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200  transition"
          >
            <PointMapIcon className="h-4 w-4" />
            Locate on Map
          </button>
        </div>
        {isMapVisible && (
          <div className="mt-4 border rounded-md overflow-hidden">
            <div style={{ width: '100%', height: 400 }}>
              <Map
                initialViewState={{
                  latitude: pickedCoords?.lat || 51.505,
                  longitude: pickedCoords?.lng || -0.09,
                  zoom: 5,
                }}
                dragPan
                dragRotate
                scrollZoom
                touchZoomRotate
                style={{ width: '100%', height: 400 }}
                mapStyle="mapbox://styles/mapbox/streets-v11"
                mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}
                onClick={handleMapClick}
              >
                {pickedCoords && (
                  <Marker
                    latitude={pickedCoords.lat}
                    longitude={pickedCoords.lng}
                  >
                    <div
                      style={{
                        background: 'red',
                        borderRadius: '50%',
                        width: 20,
                        height: 20,
                        border: '2px solid white',
                        // pointerEvents: 'none',
                      }}
                    />
                  </Marker>
                )}
              </Map>
            </div>
          </div>
        )}
      </div>

      {/* Travel Destination */}
      <div>
        <label className="block text-sm text-gray mb-1">
          Travel Destination
        </label>
        <div className="relative">
          <Input
            labelPlacement="outside"
            placeholder="Enter location"
            endContent={<MagniferIcon />}
            value={destination}
            onChange={e => setDestination(e.target.value)}
            type="text"
            variant="bordered"
          />
        </div>

        {/* Destination Tags */}
        <div className="mt-3 flex gap-2 flex-wrap">
          {destinations.map(loc => (
            <span
              key={loc.destination_id}
              role="button"
              tabIndex={0}
              className="flex items-center gap-1 rounded-md bg-primary-200/10 text-primary-200 px-3 py-1 text-sm cursor-pointer"
              onClick={() => setDestination(loc.name)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setDestination(loc.name);
                }
              }}
            >
              {loc.name}

              <button
                type="button"
                onClick={() => handleRemove(loc.destination_id)}
              >
                <X className="h-3.5 w-3.5" />
              </button>
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
