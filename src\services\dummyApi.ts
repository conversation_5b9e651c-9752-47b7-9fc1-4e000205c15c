import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Types for the dummy API response
interface Product {
  id: number;
  title: string;
  description: string;
  price: number;
  discountPercentage: number;
  rating: number;
  stock: number;
  brand: string;
  category: string;
  thumbnail: string;
  images: string[];
}

interface ProductsResponse {
  products: Product[];
  total: number;
  skip: number;
  limit: number;
}

export const dummyApi = createApi({
  reducerPath: 'dummyData',
  baseQuery: fetchBaseQuery({ baseUrl: 'https://dummyjson.com' }),
  endpoints: builder => ({
    getAllProducts: builder.query<ProductsResponse, void>({
      query: () => '/products',
    }),
  }),
});

export const { useGetAllProductsQuery } = dummyApi;
